#!/usr/bin/env python
# coding: utf-8

# Demo script for Vision Transformer on CIFAR-10
# Shows the complete pipeline from data loading to training and evaluation

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from model.vision_transformer import build_vision_transformer
from cifar10_dataloader import get_cifar10_loaders
import time

def demo_model_architecture():
    """Demonstrate the Vision Transformer architecture."""
    print("=" * 60)
    print("Vision Transformer Architecture Demo")
    print("=" * 60)
    
    # Create model
    model = build_vision_transformer(
        img_size=32,
        patch_size=4,
        in_channels=3,
        num_classes=10,
        d_model=256,
        num_layers=6,
        num_heads=8,
        d_ff=1024,
        dropout=0.1
    )
    
    # Model statistics
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"Model Configuration:")
    print(f"  Image Size: 32×32")
    print(f"  Patch Size: 4×4")
    print(f"  Number of Patches: {(32//4)**2}")
    print(f"  Model Dimension: 256")
    print(f"  Number of Layers: 6")
    print(f"  Number of Heads: 8")
    print(f"  Feed-Forward Dimension: 1024")
    print(f"  Total Parameters: {total_params:,}")
    print(f"  Trainable Parameters: {trainable_params:,}")
    print()
    
    return model


def demo_data_processing():
    """Demonstrate CIFAR-10 data processing."""
    print("=" * 60)
    print("CIFAR-10 Data Processing Demo")
    print("=" * 60)
    
    # Load data
    train_loader, test_loader, class_names = get_cifar10_loaders(batch_size=8, num_workers=2)
    
    print(f"Dataset Information:")
    print(f"  Training Samples: 50,000")
    print(f"  Test Samples: 10,000")
    print(f"  Number of Classes: {len(class_names)}")
    print(f"  Classes: {class_names}")
    print()
    
    # Get a sample batch
    for images, labels in train_loader:
        print(f"Sample Batch:")
        print(f"  Images Shape: {images.shape}")
        print(f"  Labels Shape: {labels.shape}")
        print(f"  Image Range: [{images.min():.3f}, {images.max():.3f}]")
        print(f"  Sample Labels: {labels[:5].tolist()}")
        print(f"  Sample Classes: {[class_names[i] for i in labels[:5]]}")
        break
    
    print()
    return train_loader, test_loader, class_names


def demo_forward_pass(model, train_loader):
    """Demonstrate a forward pass through the model."""
    print("=" * 60)
    print("Forward Pass Demo")
    print("=" * 60)
    
    model.eval()
    
    # Get a batch
    for images, labels in train_loader:
        print(f"Input Processing:")
        print(f"  Input Images: {images.shape}")
        
        # Forward pass
        with torch.no_grad():
            outputs = model(images)
        
        print(f"  Model Outputs: {outputs.shape}")
        print(f"  Output Range: [{outputs.min():.3f}, {outputs.max():.3f}]")
        
        # Get predictions
        _, predicted = torch.max(outputs, 1)
        print(f"  Predictions: {predicted[:5].tolist()}")
        print(f"  Actual Labels: {labels[:5].tolist()}")
        
        # Calculate accuracy
        accuracy = (predicted == labels).float().mean()
        print(f"  Random Accuracy: {accuracy:.4f}")
        
        break
    
    print()


def demo_training_step(model, train_loader):
    """Demonstrate a training step."""
    print("=" * 60)
    print("Training Step Demo")
    print("=" * 60)
    
    # Setup training
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    
    model.train()
    
    # Training step
    for images, labels in train_loader:
        # Forward pass
        optimizer.zero_grad()
        outputs = model(images)
        loss = criterion(outputs, labels)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        # Calculate accuracy
        _, predicted = torch.max(outputs, 1)
        accuracy = (predicted == labels).float().mean()
        
        print(f"Training Step Results:")
        print(f"  Loss: {loss.item():.4f}")
        print(f"  Accuracy: {accuracy.item():.4f}")
        print(f"  Gradient Norm: {torch.nn.utils.clip_grad_norm_(model.parameters(), float('inf')):.4f}")
        
        break
    
    print()


def demo_patch_visualization(train_loader):
    """Visualize how images are divided into patches."""
    print("=" * 60)
    print("Patch Visualization Demo")
    print("=" * 60)
    
    # Get a sample image
    for images, labels in train_loader:
        # Take first image and denormalize for visualization
        img = images[0]
        
        # Denormalize
        mean = torch.tensor([0.4914, 0.4822, 0.4465]).view(3, 1, 1)
        std = torch.tensor([0.2023, 0.1994, 0.2010]).view(3, 1, 1)
        img_denorm = img * std + mean
        img_denorm = torch.clamp(img_denorm, 0, 1)
        
        # Convert to numpy for plotting
        img_np = img_denorm.permute(1, 2, 0).numpy()
        
        # Create patch visualization
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Original image
        axes[0].imshow(img_np)
        axes[0].set_title('Original Image (32×32)')
        axes[0].axis('off')
        
        # Show patch grid
        axes[1].imshow(img_np)
        axes[1].set_title('Patch Grid (4×4 patches)')
        
        # Draw grid lines
        for i in range(0, 33, 4):
            axes[1].axhline(y=i-0.5, color='red', linewidth=1)
            axes[1].axvline(x=i-0.5, color='red', linewidth=1)
        axes[1].set_xlim(-0.5, 31.5)
        axes[1].set_ylim(31.5, -0.5)
        
        # Show individual patches
        patch_size = 4
        patches = []
        for i in range(0, 32, patch_size):
            for j in range(0, 32, patch_size):
                patch = img_np[i:i+patch_size, j:j+patch_size]
                patches.append(patch)
        
        # Display some patches
        patch_grid = np.zeros((32, 32, 3))
        idx = 0
        for i in range(0, 32, patch_size):
            for j in range(0, 32, patch_size):
                if idx < 16:  # Show first 16 patches
                    patch_grid[i:i+patch_size, j:j+patch_size] = patches[idx]
                idx += 1
        
        axes[2].imshow(patch_grid)
        axes[2].set_title('First 16 Patches')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.savefig('save/vit_models/patch_visualization.png', dpi=150, bbox_inches='tight')
        print("Patch visualization saved to save/vit_models/patch_visualization.png")
        plt.show()
        
        break
    
    print()


def main():
    """Run complete Vision Transformer demo."""
    print("🚀 Vision Transformer CIFAR-10 Demo")
    print("This demo showcases the complete ViT implementation pipeline")
    print()
    
    # Set random seed
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Create save directory
    import os
    os.makedirs('save/vit_models', exist_ok=True)
    
    # Demo components
    model = demo_model_architecture()
    train_loader, test_loader, class_names = demo_data_processing()
    demo_forward_pass(model, train_loader)
    demo_training_step(model, train_loader)
    demo_patch_visualization(train_loader)
    
    print("=" * 60)
    print("Demo Complete! 🎉")
    print()
    print("Next Steps:")
    print("1. Run full training: python train_vit_cifar10.py")
    print("2. Experiment with different configurations")
    print("3. Try different patch sizes or model dimensions")
    print("4. Implement additional data augmentations")
    print("=" * 60)


if __name__ == "__main__":
    main()
