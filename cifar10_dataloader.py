# CIFAR-10 Data Loader for Vision Transformer
# Handles data loading, preprocessing, and augmentation for CIFAR-10 dataset

import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader
import numpy as np


class CIFAR10DataLoader:
    """
    CIFAR-10 data loader with appropriate preprocessing for Vision Transformer.
    """
    
    def __init__(self, batch_size=64, num_workers=4, data_dir='./data/cifar10'):
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.data_dir = data_dir
        
        # CIFAR-10 class names
        self.classes = ('plane', 'car', 'bird', 'cat', 'deer', 
                       'dog', 'frog', 'horse', 'ship', 'truck')
        
        # Define transforms
        self.train_transform = transforms.Compose([
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.4914, 0.4822, 0.4465], 
                               std=[0.2023, 0.1994, 0.2010])
        ])
        
        self.test_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.4914, 0.4822, 0.4465], 
                               std=[0.2023, 0.1994, 0.2010])
        ])
        
        # Load datasets
        self.train_dataset = torchvision.datasets.CIFAR10(
            root=self.data_dir, 
            train=True, 
            download=True, 
            transform=self.train_transform
        )
        
        self.test_dataset = torchvision.datasets.CIFAR10(
            root=self.data_dir, 
            train=False, 
            download=True, 
            transform=self.test_transform
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            self.train_dataset, 
            batch_size=batch_size, 
            shuffle=True, 
            num_workers=num_workers,
            pin_memory=True
        )
        
        self.test_loader = DataLoader(
            self.test_dataset, 
            batch_size=batch_size, 
            shuffle=False, 
            num_workers=num_workers,
            pin_memory=True
        )
        
        print(f"CIFAR-10 dataset loaded:")
        print(f"  Training samples: {len(self.train_dataset)}")
        print(f"  Test samples: {len(self.test_dataset)}")
        print(f"  Number of classes: {len(self.classes)}")
        print(f"  Batch size: {batch_size}")
    
    def get_train_loader(self):
        """Return training data loader."""
        return self.train_loader
    
    def get_test_loader(self):
        """Return test data loader."""
        return self.test_loader
    
    def get_class_names(self):
        """Return class names."""
        return self.classes
    
    def get_sample_batch(self):
        """Get a sample batch for testing."""
        data_iter = iter(self.train_loader)
        images, labels = next(data_iter)
        return images, labels
    
    def denormalize(self, tensor):
        """
        Denormalize tensor for visualization.
        
        Args:
            tensor: Normalized tensor of shape (C, H, W) or (B, C, H, W)
        
        Returns:
            Denormalized tensor
        """
        mean = torch.tensor([0.4914, 0.4822, 0.4465])
        std = torch.tensor([0.2023, 0.1994, 0.2010])
        
        if tensor.dim() == 4:  # Batch of images
            mean = mean.view(1, 3, 1, 1)
            std = std.view(1, 3, 1, 1)
        else:  # Single image
            mean = mean.view(3, 1, 1)
            std = std.view(3, 1, 1)
        
        return tensor * std + mean


def get_cifar10_loaders(batch_size=64, num_workers=4, data_dir='./data/cifar10'):
    """
    Convenience function to get CIFAR-10 data loaders.
    
    Args:
        batch_size: Batch size for data loaders
        num_workers: Number of worker processes for data loading
        data_dir: Directory to store/load CIFAR-10 data
    
    Returns:
        tuple: (train_loader, test_loader, class_names)
    """
    data_loader = CIFAR10DataLoader(batch_size, num_workers, data_dir)
    return data_loader.get_train_loader(), data_loader.get_test_loader(), data_loader.get_class_names()


def visualize_samples(data_loader, num_samples=8):
    """
    Visualize sample images from the data loader.
    
    Args:
        data_loader: CIFAR10DataLoader instance
        num_samples: Number of samples to visualize
    """
    import matplotlib.pyplot as plt
    
    # Get a batch of images
    images, labels = data_loader.get_sample_batch()
    
    # Denormalize images for visualization
    images = data_loader.denormalize(images)
    
    # Create subplot
    fig, axes = plt.subplots(2, 4, figsize=(12, 6))
    axes = axes.ravel()
    
    for i in range(min(num_samples, len(images))):
        img = images[i].permute(1, 2, 0).numpy()
        img = np.clip(img, 0, 1)  # Ensure values are in [0, 1]
        
        axes[i].imshow(img)
        axes[i].set_title(f'Class: {data_loader.classes[labels[i]]}')
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    # Test the data loader
    print("Testing CIFAR-10 Data Loader...")
    
    # Create data loader
    data_loader = CIFAR10DataLoader(batch_size=32)
    
    # Test getting a batch
    train_loader = data_loader.get_train_loader()
    for batch_idx, (images, labels) in enumerate(train_loader):
        print(f"Batch {batch_idx}: Images shape: {images.shape}, Labels shape: {labels.shape}")
        if batch_idx == 0:  # Only test first batch
            break
    
    # Visualize samples
    print("Visualizing sample images...")
    visualize_samples(data_loader)
