#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import time
import numpy as np
import matplotlib.pyplot as plt
import os
from collections import defaultdict

from model.vision_transformer import build_vision_transformer
from train_vit_cifar10 import get_cifar10_loaders as get_vit_loaders, calculate_accuracy, evaluate_model
from train_resnet_cifar10 import get_cifar10_loaders as get_resnet_loaders, get_resnet_model

import warnings
warnings.filterwarnings('ignore')


def benchmark_model(model, test_loader, device, num_runs=100):
    model.eval()
    times = []
    
    with torch.no_grad():
        for images, _ in test_loader:
            images = images.to(device)
            
            for _ in range(num_runs):
                start_time = time.time()
                _ = model(images)
                torch.cuda.synchronize() if device.type == 'cuda' else None
                end_time = time.time()
                times.append(end_time - start_time)
            break
    
    return np.mean(times), np.std(times)


def count_parameters(model):
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params


def get_model_size_mb(model):
    param_size = 0
    buffer_size = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_mb = (param_size + buffer_size) / 1024 / 1024
    return size_mb


def compare_models():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    print("Loading CIFAR-10 datasets...")
    vit_train_loader, vit_test_loader, _ = get_vit_loaders(batch_size=64, num_workers=4)
    resnet_train_loader, resnet_test_loader, _ = get_resnet_loaders(batch_size=64, num_workers=4)
    
    print("\nBuilding models...")
    vit_model = build_vision_transformer(
        img_size=32,
        patch_size=4,
        in_channels=3,
        num_classes=10,
        d_model=256,
        num_layers=6,
        num_heads=8,
        d_ff=1024,
        dropout=0.1
    ).to(device)
    
    resnet_model = get_resnet_model(num_classes=10).to(device)
    
    print("\n" + "="*60)
    print("MODEL ARCHITECTURE COMPARISON")
    print("="*60)
    
    vit_total, vit_trainable = count_parameters(vit_model)
    resnet_total, resnet_trainable = count_parameters(resnet_model)
    vit_size = get_model_size_mb(vit_model)
    resnet_size = get_model_size_mb(resnet_model)
    
    print(f"Vision Transformer (ViT):")
    print(f"  Total Parameters: {vit_total:,}")
    print(f"  Trainable Parameters: {vit_trainable:,}")
    print(f"  Model Size: {vit_size:.2f} MB")
    
    print(f"\nResNet-50:")
    print(f"  Total Parameters: {resnet_total:,}")
    print(f"  Trainable Parameters: {resnet_trainable:,}")
    print(f"  Model Size: {resnet_size:.2f} MB")
    
    print(f"\nParameter Ratio (ResNet/ViT): {resnet_total/vit_total:.2f}x")
    print(f"Size Ratio (ResNet/ViT): {resnet_size/vit_size:.2f}x")
    
    print("\n" + "="*60)
    print("INFERENCE SPEED COMPARISON")
    print("="*60)
    
    print("Benchmarking ViT inference speed...")
    vit_mean_time, vit_std_time = benchmark_model(vit_model, vit_test_loader, device)
    
    print("Benchmarking ResNet inference speed...")
    resnet_mean_time, resnet_std_time = benchmark_model(resnet_model, resnet_test_loader, device)
    
    print(f"ViT Inference Time: {vit_mean_time*1000:.2f} ± {vit_std_time*1000:.2f} ms")
    print(f"ResNet Inference Time: {resnet_mean_time*1000:.2f} ± {resnet_std_time*1000:.2f} ms")
    print(f"Speed Ratio (ViT/ResNet): {vit_mean_time/resnet_mean_time:.2f}x")
    
    print("\n" + "="*60)
    print("ACCURACY COMPARISON (Random Initialization)")
    print("="*60)
    
    criterion = nn.CrossEntropyLoss()
    
    vit_loss, vit_acc = evaluate_model(vit_model, vit_test_loader, criterion, device)
    resnet_loss, resnet_acc = evaluate_model(resnet_model, resnet_test_loader, criterion, device)
    
    print(f"ViT (Random): Loss = {vit_loss:.4f}, Accuracy = {vit_acc:.4f}")
    print(f"ResNet (Pre-trained): Loss = {resnet_loss:.4f}, Accuracy = {resnet_acc:.4f}")
    
    results = {
        'vit': {
            'parameters': vit_total,
            'size_mb': vit_size,
            'inference_time_ms': vit_mean_time * 1000,
            'accuracy': vit_acc,
            'loss': vit_loss
        },
        'resnet': {
            'parameters': resnet_total,
            'size_mb': resnet_size,
            'inference_time_ms': resnet_mean_time * 1000,
            'accuracy': resnet_acc,
            'loss': resnet_loss
        }
    }
    
    create_comparison_plots(results)
    
    return results


def create_comparison_plots(results):
    os.makedirs('save/comparison', exist_ok=True)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    models = ['ViT', 'ResNet-50']
    
    parameters = [results['vit']['parameters']/1e6, results['resnet']['parameters']/1e6]
    axes[0, 0].bar(models, parameters, color=['blue', 'red'], alpha=0.7)
    axes[0, 0].set_title('Model Parameters (Millions)')
    axes[0, 0].set_ylabel('Parameters (M)')
    for i, v in enumerate(parameters):
        axes[0, 0].text(i, v + 0.5, f'{v:.1f}M', ha='center', va='bottom')
    
    sizes = [results['vit']['size_mb'], results['resnet']['size_mb']]
    axes[0, 1].bar(models, sizes, color=['blue', 'red'], alpha=0.7)
    axes[0, 1].set_title('Model Size (MB)')
    axes[0, 1].set_ylabel('Size (MB)')
    for i, v in enumerate(sizes):
        axes[0, 1].text(i, v + 1, f'{v:.1f}MB', ha='center', va='bottom')
    
    times = [results['vit']['inference_time_ms'], results['resnet']['inference_time_ms']]
    axes[1, 0].bar(models, times, color=['blue', 'red'], alpha=0.7)
    axes[1, 0].set_title('Inference Time (ms)')
    axes[1, 0].set_ylabel('Time (ms)')
    for i, v in enumerate(times):
        axes[1, 0].text(i, v + 0.1, f'{v:.2f}ms', ha='center', va='bottom')
    
    accuracies = [results['vit']['accuracy']*100, results['resnet']['accuracy']*100]
    axes[1, 1].bar(models, accuracies, color=['blue', 'red'], alpha=0.7)
    axes[1, 1].set_title('Initial Accuracy (%)')
    axes[1, 1].set_ylabel('Accuracy (%)')
    axes[1, 1].set_ylim(0, max(accuracies) * 1.2)
    for i, v in enumerate(accuracies):
        axes[1, 1].text(i, v + 1, f'{v:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('save/comparison/model_comparison.png', dpi=300, bbox_inches='tight')
    print("Comparison plots saved to save/comparison/model_comparison.png")
    plt.show()


def print_detailed_analysis():
    print("\n" + "="*80)
    print("DETAILED ANALYSIS: VISION TRANSFORMERS vs CNNs")
    print("="*80)
    
    print("\n🔍 ADVANTAGES OF VISION TRANSFORMERS:")
    print("1. Global Context Understanding:")
    print("   - Self-attention captures long-range dependencies from the start")
    print("   - Each patch can attend to all other patches directly")
    print("   - Better at understanding spatial relationships across the entire image")
    
    print("\n2. Scalability:")
    print("   - Performance improves significantly with larger datasets")
    print("   - Benefits from pre-training on massive datasets (ImageNet-21k, JFT-300M)")
    print("   - Scales well with model size and computational resources")
    
    print("\n3. Architectural Flexibility:")
    print("   - Unified architecture for various vision tasks")
    print("   - Easy to adapt for different input sizes and modalities")
    print("   - Can handle variable sequence lengths naturally")
    
    print("\n4. Transfer Learning:")
    print("   - Excellent transfer learning capabilities")
    print("   - Pre-trained features generalize well across domains")
    print("   - Less domain-specific inductive biases")
    
    print("\n⚠️ DISADVANTAGES OF VISION TRANSFORMERS:")
    print("1. Data Efficiency:")
    print("   - Requires large amounts of training data")
    print("   - Poor performance on small datasets without pre-training")
    print("   - Lacks built-in translation equivariance")
    
    print("\n2. Computational Requirements:")
    print("   - Quadratic complexity with respect to sequence length")
    print("   - Higher memory requirements during training")
    print("   - Slower inference for small images")
    
    print("\n3. Interpretability:")
    print("   - Attention maps can be noisy and hard to interpret")
    print("   - Less intuitive than CNN feature maps")
    print("   - Requires specialized visualization techniques")
    
    print("\n🔍 ADVANTAGES OF CNNs (ResNet):")
    print("1. Inductive Biases:")
    print("   - Built-in translation equivariance")
    print("   - Local connectivity matches image structure")
    print("   - Hierarchical feature learning (edges → textures → objects)")
    
    print("\n2. Data Efficiency:")
    print("   - Works well with smaller datasets")
    print("   - Strong performance even without pre-training")
    print("   - Efficient use of training data")
    
    print("\n3. Computational Efficiency:")
    print("   - Linear complexity with image size")
    print("   - Faster inference, especially for small images")
    print("   - Lower memory requirements")
    
    print("\n4. Interpretability:")
    print("   - Feature maps are intuitive and interpretable")
    print("   - Clear hierarchical feature progression")
    print("   - Well-understood activation patterns")
    
    print("\n⚠️ DISADVANTAGES OF CNNs:")
    print("1. Limited Receptive Field:")
    print("   - Requires deep networks for global context")
    print("   - Difficulty capturing long-range dependencies")
    print("   - Gradual expansion of receptive field")
    
    print("\n2. Fixed Architecture:")
    print("   - Less flexible for different input sizes")
    print("   - Requires architectural modifications for different tasks")
    print("   - Strong inductive biases can be limiting")
    
    print("\n3. Scalability Limitations:")
    print("   - Performance plateaus with very large datasets")
    print("   - Less benefit from massive pre-training")
    print("   - Architectural complexity increases with performance needs")
    
    print("\n📊 CIFAR-10 SPECIFIC CONSIDERATIONS:")
    print("- Small image size (32×32) favors CNNs")
    print("- Limited training data (50k images) challenges ViTs")
    print("- ResNet's pre-training on ImageNet provides significant advantage")
    print("- ViT patch size (4×4) creates only 64 patches, limiting self-attention benefits")
    
    print("\n🎯 RECOMMENDATIONS:")
    print("- Use CNNs for: Small datasets, limited compute, real-time applications")
    print("- Use ViTs for: Large datasets, transfer learning, research applications")
    print("- Consider hybrid approaches for optimal performance")


if __name__ == "__main__":
    results = compare_models()
    print_detailed_analysis()
