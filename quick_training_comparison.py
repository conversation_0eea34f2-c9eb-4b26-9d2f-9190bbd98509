#!/usr/bin/env python
# coding: utf-8

import torch
import torch.nn as nn
import torch.optim as optim
import time
import matplotlib.pyplot as plt
import os

from model.vision_transformer import build_vision_transformer
from train_vit_cifar10 import get_cifar10_loaders as get_vit_loaders, calculate_accuracy, evaluate_model
from train_resnet_cifar10 import get_cifar10_loaders as get_resnet_loaders, get_resnet_model

import warnings
warnings.filterwarnings('ignore')


def quick_train_model(model, train_loader, test_loader, device, model_name, num_epochs=5):
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(model.parameters(), lr=1e-3, weight_decay=1e-4)
    
    train_accuracies = []
    test_accuracies = []
    train_times = []
    
    print(f"\nTraining {model_name} for {num_epochs} epochs...")
    
    for epoch in range(num_epochs):
        model.train()
        epoch_accuracy = 0
        num_batches = 0
        
        start_time = time.time()
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            if batch_idx >= 100:  # Limit to 100 batches per epoch for quick comparison
                break
                
            images, labels = images.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            
            accuracy = calculate_accuracy(outputs, labels)
            epoch_accuracy += accuracy
            num_batches += 1
        
        epoch_time = time.time() - start_time
        avg_train_accuracy = epoch_accuracy / num_batches
        
        # Quick evaluation on subset of test data
        model.eval()
        test_accuracy = 0
        test_batches = 0
        with torch.no_grad():
            for batch_idx, (images, labels) in enumerate(test_loader):
                if batch_idx >= 20:  # Limit test evaluation
                    break
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                test_accuracy += calculate_accuracy(outputs, labels)
                test_batches += 1
        
        avg_test_accuracy = test_accuracy / test_batches
        
        train_accuracies.append(avg_train_accuracy)
        test_accuracies.append(avg_test_accuracy)
        train_times.append(epoch_time)
        
        print(f"  Epoch {epoch+1}: Train Acc = {avg_train_accuracy:.4f}, "
              f"Test Acc = {avg_test_accuracy:.4f}, Time = {epoch_time:.2f}s")
    
    return train_accuracies, test_accuracies, train_times


def run_training_comparison():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load datasets
    print("Loading datasets...")
    vit_train_loader, vit_test_loader, _ = get_vit_loaders(batch_size=64, num_workers=4)
    resnet_train_loader, resnet_test_loader, _ = get_resnet_loaders(batch_size=64, num_workers=4)
    
    # Build models
    print("Building models...")
    vit_model = build_vision_transformer(
        img_size=32, patch_size=4, in_channels=3, num_classes=10,
        d_model=256, num_layers=6, num_heads=8, d_ff=1024, dropout=0.1
    ).to(device)
    
    resnet_model = get_resnet_model(num_classes=10).to(device)
    
    # Train both models
    num_epochs = 5
    
    vit_train_acc, vit_test_acc, vit_times = quick_train_model(
        vit_model, vit_train_loader, vit_test_loader, device, "ViT", num_epochs
    )
    
    resnet_train_acc, resnet_test_acc, resnet_times = quick_train_model(
        resnet_model, resnet_train_loader, resnet_test_loader, device, "ResNet-50", num_epochs
    )
    
    # Create comparison plots
    create_training_comparison_plots(
        vit_train_acc, vit_test_acc, vit_times,
        resnet_train_acc, resnet_test_acc, resnet_times,
        num_epochs
    )
    
    # Print summary
    print("\n" + "="*60)
    print("TRAINING COMPARISON SUMMARY")
    print("="*60)
    
    print(f"Final Training Accuracy:")
    print(f"  ViT: {vit_train_acc[-1]:.4f}")
    print(f"  ResNet-50: {resnet_train_acc[-1]:.4f}")
    
    print(f"\nFinal Test Accuracy:")
    print(f"  ViT: {vit_test_acc[-1]:.4f}")
    print(f"  ResNet-50: {resnet_test_acc[-1]:.4f}")
    
    print(f"\nAverage Training Time per Epoch:")
    print(f"  ViT: {sum(vit_times)/len(vit_times):.2f}s")
    print(f"  ResNet-50: {sum(resnet_times)/len(resnet_times):.2f}s")
    
    print(f"\nAccuracy Improvement (First to Last Epoch):")
    print(f"  ViT: {vit_test_acc[-1] - vit_test_acc[0]:.4f}")
    print(f"  ResNet-50: {resnet_test_acc[-1] - resnet_test_acc[0]:.4f}")


def create_training_comparison_plots(vit_train_acc, vit_test_acc, vit_times,
                                   resnet_train_acc, resnet_test_acc, resnet_times,
                                   num_epochs):
    os.makedirs('save/comparison', exist_ok=True)
    
    epochs = range(1, num_epochs + 1)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Training accuracy comparison
    axes[0, 0].plot(epochs, vit_train_acc, 'b-o', label='ViT', linewidth=2, markersize=6)
    axes[0, 0].plot(epochs, resnet_train_acc, 'r-s', label='ResNet-50', linewidth=2, markersize=6)
    axes[0, 0].set_title('Training Accuracy Comparison')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Accuracy')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Test accuracy comparison
    axes[0, 1].plot(epochs, vit_test_acc, 'b-o', label='ViT', linewidth=2, markersize=6)
    axes[0, 1].plot(epochs, resnet_test_acc, 'r-s', label='ResNet-50', linewidth=2, markersize=6)
    axes[0, 1].set_title('Test Accuracy Comparison')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('Accuracy')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Training time comparison
    axes[1, 0].bar(['ViT', 'ResNet-50'], 
                   [sum(vit_times)/len(vit_times), sum(resnet_times)/len(resnet_times)],
                   color=['blue', 'red'], alpha=0.7)
    axes[1, 0].set_title('Average Training Time per Epoch')
    axes[1, 0].set_ylabel('Time (seconds)')
    
    # Accuracy improvement
    vit_improvement = vit_test_acc[-1] - vit_test_acc[0]
    resnet_improvement = resnet_test_acc[-1] - resnet_test_acc[0]
    
    axes[1, 1].bar(['ViT', 'ResNet-50'], 
                   [vit_improvement, resnet_improvement],
                   color=['blue', 'red'], alpha=0.7)
    axes[1, 1].set_title('Accuracy Improvement (5 Epochs)')
    axes[1, 1].set_ylabel('Accuracy Gain')
    
    # Add value labels on bars
    for ax in [axes[1, 0], axes[1, 1]]:
        for i, bar in enumerate(ax.patches):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{height:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('save/comparison/training_comparison.png', dpi=300, bbox_inches='tight')
    print("Training comparison plots saved to save/comparison/training_comparison.png")
    plt.show()


def print_key_insights():
    print("\n" + "="*80)
    print("KEY INSIGHTS FROM CIFAR-10 COMPARISON")
    print("="*80)
    
    print("\n📊 PERFORMANCE OBSERVATIONS:")
    print("1. ResNet-50 benefits significantly from ImageNet pre-training")
    print("2. ViT shows faster inference despite larger computational complexity")
    print("3. Both models show similar learning curves on this small dataset")
    print("4. ResNet has 5x more parameters but may converge faster initially")
    
    print("\n🎯 PRACTICAL IMPLICATIONS:")
    print("1. For CIFAR-10 specifically:")
    print("   - ResNet's pre-training gives immediate advantage")
    print("   - ViT requires more training to reach competitive performance")
    print("   - Small image size (32x32) limits ViT's patch-based approach")
    
    print("\n2. For real-world applications:")
    print("   - Use pre-trained ResNet for quick deployment")
    print("   - Consider ViT for larger datasets or when pre-trained ViT available")
    print("   - Hybrid approaches may offer best of both worlds")
    
    print("\n3. Resource considerations:")
    print("   - ViT: Lower memory, faster inference, needs more training data")
    print("   - ResNet: Higher memory, established training procedures, works with less data")


if __name__ == "__main__":
    run_training_comparison()
    print_key_insights()
