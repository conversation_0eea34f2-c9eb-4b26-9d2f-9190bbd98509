# Vision Transformer (ViT) Implementation for CIFAR-10

This document provides a comprehensive guide to the Vision Transformer implementation for CIFAR-10 image classification, built upon the existing Transformer architecture from the English-to-Chinese translation project.

## Overview

The Vision Transformer (ViT) adapts the Transformer architecture for computer vision tasks by treating images as sequences of patches. This implementation demonstrates how to modify the existing NLP Transformer for image classification on the CIFAR-10 dataset.

## Key Components

### 1. Patch Embedding (`model/vision_transformer.py`)

The `PatchEmbedding` class converts 32×32 CIFAR-10 images into sequences of patch embeddings:

- **Input**: Images of shape `(batch_size, 3, 32, 32)`
- **Process**: Divides images into 4×4 patches (64 patches total)
- **Output**: Patch embeddings of shape `(batch_size, 64, d_model)`

```python
# For CIFAR-10: 32×32 image with 4×4 patches
num_patches = (32 // 4)² = 64 patches
patch_dim = 4 × 4 × 3 = 48 features per patch
```

### 2. Vision Transformer Architecture

The `VisionTransformer` class implements the complete ViT model:

**Key Features:**
- **Learnable Class Token**: Added to the sequence for classification
- **Learnable Positional Embeddings**: 2D-aware position encoding
- **Transformer Encoder**: Reuses existing encoder blocks
- **Classification Head**: Maps class token to 10 CIFAR-10 classes

**Architecture Flow:**
1. Image → Patch Embeddings
2. Add Class Token
3. Add Positional Embeddings
4. Transformer Encoder (6 layers)
5. Extract Class Token
6. Classification Head → 10 classes

### 3. Data Loading (`cifar10_dataloader.py`)

The `CIFAR10DataLoader` class handles:
- **Data Augmentation**: Random flips, rotations, color jitter
- **Normalization**: CIFAR-10 specific mean/std normalization
- **Batch Processing**: Efficient data loading with PyTorch DataLoader

### 4. Training Script (`train_vit_cifar10.py`)

Comprehensive training pipeline with:
- **Loss Function**: CrossEntropyLoss for classification
- **Optimizer**: AdamW with weight decay
- **Scheduler**: Cosine annealing learning rate
- **Metrics**: Loss and accuracy tracking
- **Visualization**: Training curves plotting

## Model Configuration

### Debug Configuration (Quick Testing)
```python
config = {
    'batch_size': 64,
    'num_epochs': 20,
    'd_model': 256,
    'num_layers': 6,
    'num_heads': 8,
    'd_ff': 1024,
    'lr': 1e-3
}
```

### Production Configuration (Full Training)
```python
config = {
    'batch_size': 128,
    'num_epochs': 100,
    'd_model': 512,
    'num_layers': 12,
    'num_heads': 8,
    'd_ff': 2048,
    'lr': 1e-4
}
```

## Usage Instructions

### 1. Test Implementation
```bash
python test_vit_implementation.py
```
This runs comprehensive tests to verify all components work correctly.

### 2. Run Training
```bash
python train_vit_cifar10.py
```
This starts the full training process with monitoring and visualization.

### 3. Monitor Progress
The training script provides:
- Real-time progress bars with loss/accuracy
- Epoch-wise performance metrics
- Automatic model checkpointing
- Training curve visualization

## Expected Results

### Model Performance
- **Parameters**: ~4.8M (debug config) / ~22M (production config)
- **Expected Accuracy**: 70-85% on CIFAR-10 test set
- **Training Time**: 20-30 minutes (debug) / 2-4 hours (production)

### Training Curves
The implementation generates plots showing:
- Training vs. Test Loss
- Training vs. Test Accuracy
- Performance progression over epochs

## Key Adaptations from NLP Transformer

### 1. Input Processing
- **NLP**: Token embeddings + positional encoding
- **ViT**: Patch embeddings + learnable positional encoding

### 2. Architecture
- **NLP**: Encoder-Decoder with attention masks
- **ViT**: Encoder-only with no masking (all patches attend to all)

### 3. Output Processing
- **NLP**: Sequence generation with vocabulary projection
- **ViT**: Classification with class token extraction

### 4. Training Objective
- **NLP**: Cross-entropy on next token prediction
- **ViT**: Cross-entropy on image classification

## File Structure
```
├── model/
│   ├── transformer.py          # Original Transformer components
│   └── vision_transformer.py   # ViT-specific components
├── cifar10_dataloader.py       # CIFAR-10 data handling
├── train_vit_cifar10.py        # Training script
├── test_vit_implementation.py  # Testing script
└── save/
    └── vit_models/             # Model checkpoints and plots
```

## Technical Details

### Patch Extraction
Images are divided into non-overlapping patches using PyTorch's `unfold` operation:
```python
x = x.unfold(2, patch_size, patch_size).unfold(3, patch_size, patch_size)
```

### Class Token
A learnable parameter that aggregates information from all patches:
```python
self.class_token = nn.Parameter(torch.randn(1, 1, d_model))
```

### Position Encoding
Learnable embeddings that encode spatial relationships:
```python
self.positional_encoding = LearnablePositionalEncoding(d_model, num_patches + 1, dropout)
```

## Performance Optimization

### Memory Efficiency
- Gradient checkpointing for large models
- Mixed precision training support
- Efficient data loading with multiple workers

### Training Stability
- Weight decay regularization
- Dropout for overfitting prevention
- Learning rate scheduling

## Troubleshooting

### Common Issues
1. **CUDA Out of Memory**: Reduce batch size or model dimensions
2. **Slow Training**: Increase num_workers in data loader
3. **Poor Convergence**: Adjust learning rate or add warmup

### Performance Tips
1. Use larger patch sizes for faster training
2. Increase model depth for better accuracy
3. Add data augmentation for improved generalization

This implementation provides a solid foundation for understanding and experimenting with Vision Transformers on CIFAR-10, while demonstrating the adaptability of the Transformer architecture across different domains.
