# Vision Transformer (ViT) Implementation for CIFAR-10 Classification
# Based on the existing Transformer architecture, adapted for computer vision tasks

import torch
import torch.nn as nn
import math
from .transformer import (
    LayerNormalization, 
    FeedForwardBlock, 
    MultiHeadAttentionBlock, 
    ResidualConnection,
    EncoderBlock,
    Encoder,
    LearnablePositionalEncoding
)


class PatchEmbedding(nn.Module):
    """
    Convert image patches to embeddings.
    For CIFAR-10: 32x32 images -> patches of size patch_size x patch_size
    """
    
    def __init__(self, img_size=32, patch_size=4, in_channels=3, d_model=256):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_channels = in_channels
        self.d_model = d_model
        
        # Calculate number of patches
        self.num_patches = (img_size // patch_size) ** 2
        
        # Linear projection of flattened patches
        self.projection = nn.Linear(patch_size * patch_size * in_channels, d_model)
        
    def forward(self, x):
        """
        Args:
            x: Input images of shape (batch_size, channels, height, width)
        Returns:
            Patch embeddings of shape (batch_size, num_patches, d_model)
        """
        batch_size, channels, height, width = x.shape
        
        # Ensure input dimensions are correct
        assert height == self.img_size and width == self.img_size, \
            f"Input image size ({height}x{width}) doesn't match expected size ({self.img_size}x{self.img_size})"
        
        # Extract patches: (batch_size, channels, height, width) -> (batch_size, num_patches, patch_size^2 * channels)
        x = x.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)
        x = x.contiguous().view(batch_size, channels, -1, self.patch_size, self.patch_size)
        x = x.permute(0, 2, 1, 3, 4).contiguous()
        x = x.view(batch_size, self.num_patches, -1)
        
        # Apply linear projection
        x = self.projection(x)
        
        return x


class ClassificationHead(nn.Module):
    """
    Classification head for ViT.
    Takes the class token representation and outputs class probabilities.
    """
    
    def __init__(self, d_model, num_classes, dropout=0.1):
        super().__init__()
        self.dropout = nn.Dropout(dropout)
        self.classifier = nn.Linear(d_model, num_classes)
        
    def forward(self, x):
        """
        Args:
            x: Class token representation of shape (batch_size, d_model)
        Returns:
            Class logits of shape (batch_size, num_classes)
        """
        x = self.dropout(x)
        return self.classifier(x)


class VisionTransformer(nn.Module):
    """
    Vision Transformer for image classification.
    Based on the existing Transformer encoder architecture.
    """
    
    def __init__(self, 
                 img_size=32, 
                 patch_size=4, 
                 in_channels=3, 
                 num_classes=10,
                 d_model=256, 
                 num_layers=6, 
                 num_heads=8, 
                 d_ff=1024, 
                 dropout=0.1):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.num_classes = num_classes
        self.d_model = d_model
        
        # Patch embedding
        self.patch_embedding = PatchEmbedding(img_size, patch_size, in_channels, d_model)
        num_patches = self.patch_embedding.num_patches
        
        # Class token - learnable parameter
        self.class_token = nn.Parameter(torch.randn(1, 1, d_model))
        
        # Positional embeddings (learnable)
        # +1 for class token
        self.positional_encoding = LearnablePositionalEncoding(d_model, num_patches + 1, dropout)
        
        # Transformer encoder
        encoder_blocks = []
        for _ in range(num_layers):
            self_attention = MultiHeadAttentionBlock(d_model, num_heads, dropout)
            feed_forward = FeedForwardBlock(d_model, d_ff, dropout)
            encoder_block = EncoderBlock(self_attention, feed_forward, dropout)
            encoder_blocks.append(encoder_block)
        
        self.encoder = Encoder(nn.ModuleList(encoder_blocks))
        
        # Classification head
        self.classification_head = ClassificationHead(d_model, num_classes, dropout)
        
        # Initialize parameters
        self._init_parameters()
        
    def _init_parameters(self):
        """Initialize model parameters."""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
        
        # Initialize class token with small random values
        nn.init.normal_(self.class_token, mean=0, std=0.02)
    
    def forward(self, x):
        """
        Forward pass of Vision Transformer.
        
        Args:
            x: Input images of shape (batch_size, channels, height, width)
        Returns:
            Class logits of shape (batch_size, num_classes)
        """
        batch_size = x.shape[0]
        
        # Convert images to patch embeddings
        x = self.patch_embedding(x)  # (batch_size, num_patches, d_model)
        
        # Add class token
        class_tokens = self.class_token.expand(batch_size, -1, -1)  # (batch_size, 1, d_model)
        x = torch.cat([class_tokens, x], dim=1)  # (batch_size, num_patches + 1, d_model)
        
        # Add positional embeddings
        x = self.positional_encoding(x)
        
        # Pass through transformer encoder
        # No mask needed for ViT (all patches can attend to all patches)
        x = self.encoder(x, mask=None)
        
        # Extract class token representation
        class_token_output = x[:, 0]  # (batch_size, d_model)
        
        # Classification
        logits = self.classification_head(class_token_output)
        
        return logits


def build_vision_transformer(img_size=32, 
                           patch_size=4, 
                           in_channels=3, 
                           num_classes=10,
                           d_model=256, 
                           num_layers=6, 
                           num_heads=8, 
                           d_ff=1024, 
                           dropout=0.1):
    """
    Build and return a Vision Transformer model.
    
    Args:
        img_size: Input image size (CIFAR-10: 32)
        patch_size: Size of each patch (e.g., 4 for 4x4 patches)
        in_channels: Number of input channels (RGB: 3)
        num_classes: Number of output classes (CIFAR-10: 10)
        d_model: Model dimension
        num_layers: Number of transformer layers
        num_heads: Number of attention heads
        d_ff: Feed-forward dimension
        dropout: Dropout rate
    
    Returns:
        VisionTransformer model
    """
    model = VisionTransformer(
        img_size=img_size,
        patch_size=patch_size,
        in_channels=in_channels,
        num_classes=num_classes,
        d_model=d_model,
        num_layers=num_layers,
        num_heads=num_heads,
        d_ff=d_ff,
        dropout=dropout
    )
    
    return model
